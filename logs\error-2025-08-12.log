2025-08-12T09:26:53.381Z [UNHANDLED_REJECTION] {
  "timestamp": "2025-08-12T09:26:53.381Z",
  "type": "UNHANDLED_REJECTION",
  "message": "[object Object]",
  "stack": "",
  "pid": 19128,
  "platform": "win32",
  "arch": "x64",
  "nodeVersion": "v22.15.1",
  "electronVersion": "36.3.2"
}

2025-08-12T09:27:08.173Z [UNHANDLED_REJECTION] {
  "timestamp": "2025-08-12T09:27:08.173Z",
  "type": "UNHANDLED_REJECTION",
  "message": "[object Object]",
  "stack": "",
  "pid": 17056,
  "platform": "win32",
  "arch": "x64",
  "nodeVersion": "v22.15.1",
  "electronVersion": "36.3.2"
}

2025-08-12T09:33:19.113Z [UNHA<PERSON>LED_REJECTION] {
  "timestamp": "2025-08-12T09:33:19.113Z",
  "type": "UNHANDLED_REJECTION",
  "message": "[object Object]",
  "stack": "",
  "pid": 23016,
  "platform": "win32",
  "arch": "x64",
  "nodeVersion": "v22.15.1",
  "electronVersion": "36.3.2"
}

