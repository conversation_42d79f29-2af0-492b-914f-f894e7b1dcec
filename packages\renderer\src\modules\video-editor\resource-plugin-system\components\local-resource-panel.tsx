import React, { <PERSON>actN<PERSON>, useEffect, useMemo, useState } from 'react'
import { PlusIcon, Folder } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { UploadedFile } from '@/components/ui/file-uploader'
import { FolderUploadedFile } from '@/components/ui/folder-uploader'
import { TreeNode } from '@/components/TreeList'
import { cn } from '@/components/lib/utils'
import MoveDialog from '@/pages/Projects/material/components/MoveDialog'
import { useItemActions } from '@/hooks/useItemActions'
import { PasterResource, ResourceSource, SoundResource } from '@/types/resources'
import { ResourceModule } from '@/libs/request/api/resource'
import LocalDirItem from '@/modules/video-editor/resource-plugin-system/components/local-dir'
import { useSelection } from '@/hooks/useSelection'
import UploadMaterial from '@/pages/Projects/material/components/UploadMaterial'
import {
  DraggableItem,
  DroppableItem,
  pointerWithinFolder,
  centerDragOverlay,
  useFolderDndSensors,
  useFolderDragEnd
} from '@/components/folder-dnd'
import { DndContext, DragOverlay } from '@dnd-kit/core'
import { useFolderActions } from '@/hooks/useFolderActions'
import { useMediaActions } from '@/hooks/useMediaActions'
import { useFolderData } from '@/hooks/useFolderData'
import { Music } from 'lucide-react'

export type Folder = {
  id: string
  name: string
}

export interface LocalResourcePanelProps<T> {
  /**
   * 目录数据
   */
  dirList: TreeNode[]
  /**
   * 当前选中的目录
   */
  currentFolderId: string
  /**
   * 切换目录回调
   */
  onFolderChange: (folderId: string) => void // 新增: 切换目录回调
  /**
   * 本地资源类型
   */
  resourceType: ResourceSource
  /**
   * 本地资源文件夹类型
   */
  resourceFolderType: ResourceSource
  /**
   * 文件上传类型
   */
  fileUploadTypes: string[]
  /**
   * 关键词
   */
  searchKey?: string
  /**
   * 上传完成回调
   */
  onUploadComplete?: (uploaded: (FolderUploadedFile | UploadedFile)[]) => void
  /**
   * 添加进轨道回调
   */
  onAddToTimeline?: (resource: T) => void
  /**
   * 资源列表
   */
  resources?: any
  /**
   * 是否显示上传按钮
   */
  showUpload?: boolean
  /**
   * 是否显示新建文件夹按钮
   */
  showCreateFolder?: boolean
  /**
   * 列表为空时的提示文本
   */
  emptyText?: string
  /**
   * 列表容器的类名
   */
  containerClassName?: string
  /**
   * 资源网格的列数
   */
  gridCols?: number
  /**
   * 是否加载中
   */
  isLoading?: boolean
  /**
   * 资源项渲染函数
   */
  renderResourceItem?: (resource: T, index: number) => ReactNode
}

/**
 * 通用本地资源面板组件
 * 提供文件夹选择、上传文件、新建文件夹功能，以及资源列表展示
 */
export function LocalResourcePanel<T extends PasterResource.PasterLocal | SoundResource.SoundLocal>({
  dirList,
  currentFolderId,
  onFolderChange,
  resourceType,
  resourceFolderType,
  fileUploadTypes,
  searchKey = '',
  onUploadComplete,
  onAddToTimeline,
  resources = {},
  showUpload = true,
  showCreateFolder = true,
  renderResourceItem,
  emptyText = '暂无资源',
  containerClassName = '',
  gridCols = 4,
  isLoading = false,
}: LocalResourcePanelProps<T>) {
  const [moveDialogOpen, setMoveDialogOpen] = useState(false)
  const [moveId, setMoveId] = useState('') //被移动的文件夹id
  const [moveType, setMoveType] = useState(resourceType)
  const { createItem, deleteLocalItem, moveItem, invalidate } = useItemActions()
  const flatResourcesLength = Array.isArray(resources?.pages)
    ? resources.pages.reduce((count, page) => count + (page?.list?.length || 0), 0)
    : 0
  const [processedResources, setProcessedResources] = useState<T[]>([])
  const [activeMedia, setActiveMedia] = useState<any | null>(null)
  const sensors = useFolderDndSensors()
  const handleDragEnd = useFolderDragEnd(setActiveMedia)

  //本地文件夹操作
  const LocalFolderActions = useFolderActions(true, resourceFolderType, setMoveType, setMoveId, setMoveDialogOpen)
  const LocalMediaActions = useMediaActions(true, resourceType, setMoveType, setMoveId, setMoveDialogOpen)

  const handleMoveConfirm = async (selectedNode: TreeNode) => {
    // 移动媒体文件
    if (selectedMediaItems.size > 0) {
      moveItem(moveType, Array.from(selectedMediaItems), selectedNode.id)
      await invalidate([resourceType])
      setSelectedMediaItems(new Set())
    }
  }

  // 获取根目录的目录树
  const rootDirs = useMemo(() => {
    return dirList.filter(item => item.raw?.parentId === null)
  }, [dirList])

  // 校验 currentFolderId 是否存在于根目录树，如果不存在则重置
  useEffect(() => {
    const findNodeInTree = (nodes: TreeNode[], id: string): boolean => {
      for (const node of nodes) {
        if (String(node.id) === String(id)) return true
        if (node.children && findNodeInTree(node.children, id)) return true
      }
      return false
    }

    if (!findNodeInTree(rootDirs, currentFolderId) && rootDirs.length > 0) {
      // 如果当前 folderId 不在筛选出的树中，则重置为第一个 root 目录 id
      onFolderChange(rootDirs[0].id)
    }
  }, [rootDirs, currentFolderId, onFolderChange])

  // 获取目录链
  const { folderPath, childFolders: originalChildFolders } = useFolderData({
    currentFolderId,
    treeData: rootDirs,
  })

  // 获取当前目录下的子目录根据筛选词筛选
  const childFolders = useMemo(() => {
    if (!searchKey) return originalChildFolders
    const keyword = searchKey.toLowerCase()
    return originalChildFolders.filter(child => child.label?.toLowerCase().includes(keyword))
  }, [searchKey, originalChildFolders])

  const {
    selectedMediaItems,
    setSelectedMediaItems,
    toggleSelect,
    toggleSelectAll,
    allSelected,
    selectedCount,
    materialCount,
  } = useSelection({
    mediaList: resources,
    getMediaId: (media: { fileId: string }) => media.fileId,
  })

  const extractFileId = (url: string) => {
    try {
      const parsedUrl = new URL(url)
      const pathSegments = parsedUrl.pathname.split('/')
      return pathSegments[pathSegments.length - 1] // 获取 URL 路径中的最后一部分作为 ID
    } catch (error) {
      console.error('URL 解析失败:', error)
      return ''
    }
  }

  const processResources = async (resourceList: any[]) => {
    const replaceUrl = async (url: string) => {
      if (!url) return ''
      const objectId = extractFileId(url)
      const res = await ResourceModule.cover(objectId)
      const parser = new DOMParser()
      const doc = parser.parseFromString(res, 'text/html')
      const anchor = doc.querySelector('a')
      return anchor?.href || url // 如果解析失败，保持原 URL
    }

    return await Promise.all(
      resourceList.map(async resource => {
        try {
          if (resourceType === ResourceSource.LOCAL_STICK) {
            const fileUrl = await replaceUrl(resource.content?.fileUrl)
            const thumbUrl = await replaceUrl(resource.content?.thumbUrl)
            const coverUrl = await replaceUrl(resource.cover?.url)

            return {
              ...resource,
              content: {
                ...resource.content,
                fileUrl,
                thumbUrl,
              },
              cover: {
                ...resource.cover,
                url: coverUrl,
              },
            }
          } else if (resourceType === ResourceSource.LOCAL_SOUND || resourceType === ResourceSource.LOCAL_MUSIC) {
            const itemUrl = await replaceUrl(resource.content?.itemUrl)
            return {
              ...resource,
              content: {
                ...resource.content,
                itemUrl,
              },
            }
          }
        } catch (error) {
          console.error('获取地址失败:', error)
          return resource
        }
      }),
    )
  }

  const onLocalItemAdd = async (item: T) => {
    console.log('点击本地资源项-添加进轨道', item)

    onAddToTimeline?.(item)
  }

  const onLocalFolderAdd = async (item: TreeNode) => {
    console.log('点击本地文件夹-添加进轨道', item)
    try {
      let fetchFn
      switch (resourceType) {
        case ResourceSource.LOCAL_STICK:
          fetchFn = ResourceModule.paster.localList
          break
        case ResourceSource.LOCAL_MUSIC:
          fetchFn = ResourceModule.music.localList
          break
        case ResourceSource.LOCAL_SOUND:
          fetchFn = ResourceModule.voice.localList
          break
        default:
          console.warn('不支持的资源类型:', resourceType)
          return
      }
      const res = await fetchFn({
        pageSize: 100, // 取足够多的文件
        folderUuid: item.id,
        search: '',
        pageNum: 1,
      })

      const folderResources = res?.list || []
      const updatedResources = await processResources(folderResources)

      if (updatedResources.length > 0) {
        updatedResources.forEach(item => onAddToTimeline?.(item))
      } else {
        console.log('该文件夹下没有文件资源')
      }
    } catch (err) {
      console.error('获取文件夹资源失败:', err)
    }
  }

  //获取fileUrl 和 thumbUrl
  useEffect(() => {
    const fetchCovers = async () => {
      if (!resources?.pages) return

      const allResources = resources.pages.flatMap(page => page.list)
      const updatedResources = await processResources(allResources)

      setProcessedResources(updatedResources)
    }

    fetchCovers()
  }, [resources])

  //上传完成之后创建本地资源
  const handleUploadComplete = async (files: FolderUploadedFile[] | UploadedFile[]) => {
    const uploaded = files.filter(f => f.status === 'success')
    if (onUploadComplete) {
      onUploadComplete(uploaded)
    }
  }

  //彻底删除
  const handleDelete = async () => {
    if (selectedMediaItems.size > 0) {
      await deleteLocalItem(resourceType, Array.from(selectedMediaItems), `${selectedCount}个资源文件`)
    }

    await invalidate([resourceType])
    setSelectedMediaItems(new Set())
  }

  const handleDragCancel = () => {
    setActiveMedia(null) // 拖拽取消清空预览图片
  }

  useEffect(() => {
    setSelectedMediaItems(new Set())
  }, [currentFolderId])

  return (
    <div className={`flex flex-col gap-4 mt-2 ${containerClassName}`}>
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2 w-full justify-between">
          {showUpload && (
            <UploadMaterial
              folderUuid={currentFolderId}
              fileUploadTypes={fileUploadTypes}
              resourceType={resourceType}
              onUpload={handleUploadComplete}
            />
          )}
          {showCreateFolder && (
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() =>
                createItem(resourceFolderType, currentFolderId, {
                  label: '文件夹名称',
                  headerTitle: '文件夹',
                })}
            >
              <PlusIcon className="w-3.5 h-3.5" />
              <span>新建文件夹</span>
            </Button>
          )}
        </div>
      </div>
      {selectedCount ? (
        <div className="flex items-center justify-between text-sm ">
          <div className="flex justify-end items-center space-x-4">
            <span>总数：{materialCount}</span>
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                checked={allSelected}
                onClick={toggleSelectAll}
                readOnly
                className="accent-primary-highlight1"
              />
            </label>
            <span>已选 {selectedCount}</span>
          </div>
          <div>
            <Button
              variant="link"
              size="sm"
              className="text-primary-highlight1"
              onClick={() => {
                let type = ResourceSource.LOCAL_MUSIC
                if (resourceType === ResourceSource.LOCAL_MUSIC) {
                  type = ResourceSource.LOCAL_MUSIC_MULTI_SELECT
                } else if (resourceType === ResourceSource.LOCAL_SOUND) {
                  type = ResourceSource.LOCAL_SOUND_MULTI_SELECT
                } else if (resourceType === ResourceSource.LOCAL_STICK) {
                  type = ResourceSource.LOCAL_STICK_MULTI_SELECT
                }
                setMoveType(type)
                setMoveDialogOpen(true)
              }}
            >
              移动到
            </Button>
            <span className="text-primary-highlight1"> | </span>
            <Button variant="link" size="sm" className="text-primary-highlight1 " onClick={handleDelete}>
              删除
            </Button>
          </div>
        </div>
      ) : (
        <div>
          <div className="flex items-center space-x-1 text-white">
            {folderPath.map((folder, index) => (
              <React.Fragment key={folder.id}>
                <button
                  className={cn('hover:underline', {
                    'text-primary-highlight1': folder.id === currentFolderId,
                  })}
                  onClick={() => onFolderChange(folder.id)}
                >
                  {folder.label}
                </button>
                {index < folderPath.length - 1 && <span>{'>'}</span>}
              </React.Fragment>
            ))}
          </div>
        </div>
      )}
      {/* 资源列表区域 */}
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-400">加载中...</div>
        </div>
      ) : (
        <>
          {childFolders.length > 0 || flatResourcesLength > 0 ? (
            <div className={`grid grid-cols-${gridCols} gap-3 mb-4`}>
              <DndContext
                onDragStart={event => {
                  const { active } = event
                  if (active.data.current) {
                    setActiveMedia(active.data.current) // 记录正在拖拽的 media
                  }
                }}
                onDragCancel={handleDragCancel}
                onDragEnd={handleDragEnd}
                sensors={sensors}
                collisionDetection={pointerWithinFolder}
              >
                {/* 渲染子文件夹 */}
                {childFolders.map(folder => (
                  <DroppableItem key={`${folder.id}-folders`} id={folder.id} type={resourceFolderType}>
                    <DraggableItem<T>
                      key={`${folder.id}-folder`}
                      id={`${folder.id}`}
                      resource={folder}
                      type={resourceFolderType}
                    >
                      <LocalDirItem
                        key={folder.id}
                        folder={folder}
                        currentFolderId={currentFolderId}
                        onFolderChange={onFolderChange}
                        LocalActions={LocalFolderActions}
                        onItemAdd={() => onLocalFolderAdd(folder)}
                      />
                    </DraggableItem>
                  </DroppableItem>
                ))}
                {/* 渲染本地资源 */}
                {processedResources.map((resource, index) => (
                  <DraggableItem<T>
                    key={`${resource.fileId}-media`}
                    id={resource.fileId}
                    resource={resource}
                    type={resourceType}
                  >
                    <LocalDirItem
                      key={resource.fileId || index}
                      resource={resource}
                      isResource={true}
                      index={index}
                      currentFolderId={currentFolderId}
                      onFolderChange={onFolderChange}
                      LocalActions={LocalMediaActions}
                      renderResourceItem={renderResourceItem}
                      onItemAdd={() => onLocalItemAdd(resource)}
                      isSelected={selectedMediaItems.has(resource.fileId)}
                      onToggleSelect={toggleSelect}
                    />
                  </DraggableItem>
                ))}
                {/* 拖拽时的预览图片 */}
                <DragOverlay modifiers={[centerDragOverlay]} dropAnimation={null}>
                  {activeMedia ? (
                    activeMedia.type === resourceFolderType ? (
                      // 文件夹类型
                      <Folder className="w-[30%] h-[30%]" />
                    ) : activeMedia.contentType === 'paster' ? (
                      // 本地贴纸类型
                      <img src={activeMedia.content.fileUrl} alt={activeMedia.title} />
                    ) : (
                      // 本地音效类型和本地音乐
                      <div className="w-24 h-24 flex items-center justify-center bg-gray-200 rounded shadow-lg">
                        <Music className="w-[40%] h-[40%]" />
                      </div>
                    )
                  ) : null}
                </DragOverlay>
              </DndContext>
            </div>
          ) : (
            <div className="flex justify-center items-center h-40">
              <div className="text-gray-400">{emptyText}</div>
            </div>
          )}
        </>
      )}
      <MoveDialog
        open={moveDialogOpen}
        moveId={moveId}
        dirList={dirList}
        moveType={moveType}
        onOpenChange={setMoveDialogOpen}
        onConfirm={handleMoveConfirm}
      />
    </div>
  )
}

export default LocalResourcePanel
