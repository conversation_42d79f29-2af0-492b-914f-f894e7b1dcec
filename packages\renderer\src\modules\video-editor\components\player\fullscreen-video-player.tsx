import React, { useEffect, useRef, useState } from 'react'
import { Player } from '@remotion/player'
import { useCachedOverlaysContext, useEditorContext } from '@/modules/video-editor/contexts'
import { MainProps, Renderer } from '@clipnest/overlay-renderer'
import { EditableRenderer } from './editable-renderer'
import { Button } from '@/components/ui/button'
import { Minimize, Pause, Play, SkipBack, SkipForward, RotateCcw, X } from 'lucide-react'

const RENDER_MODE = import.meta.env.VITE_APP_PLAYER_RENDER_ONLY === 'true'

interface FullscreenVideoPlayerProps {
  isVisible: boolean
  onClose: () => void
}

/**
 * FullscreenVideoPlayer component provides a fullscreen video viewing experience
 * It reuses the existing VideoPlayer component with fullscreen-specific styling and controls
 */
export const FullscreenVideoPlayer: React.FC<FullscreenVideoPlayerProps> = ({
  isVisible,
  onClose
}) => {
  const playerWrapper = useRef<HTMLDivElement>(null)
  const fullscreenContainer = useRef<HTMLDivElement>(null)
  const [showControls, setShowControls] = useState(true)
  const [mouseTimer, setMouseTimer] = useState<NodeJS.Timeout | null>(null)
  const [isFullscreenActive, setIsFullscreenActive] = useState(false)

  const editorContext = useEditorContext()
  const {
    durationInFrames,
    getPlayerDimensions,
    videoPlayer: {
      playerRef,
      fps,
      isPlaying,
      currentFrame,
      formatTime,
      togglePlayPause,
      skip,
      resetToStart
    }
  } = editorContext

  const { overlays } = useCachedOverlaysContext()
  const { playerWidth, playerHeight } = getPlayerDimensions()

  // Handle mouse movement to show/hide controls
  const handleMouseMove = () => {
    if (!showControls) {
      setShowControls(true)
    }

    if (mouseTimer) {
      clearTimeout(mouseTimer)
    }

    const timer = setTimeout(() => {
      setShowControls(false)
    }, 3000) // Hide controls after 3 seconds of inactivity

    setMouseTimer(timer)
  }

  // Handle fullscreen API
  useEffect(() => {
    if (isVisible && fullscreenContainer.current && !isFullscreenActive) {
      // Enter fullscreen
      fullscreenContainer.current.requestFullscreen().then(() => {
        setIsFullscreenActive(true)
        // Show controls initially when entering fullscreen
        setShowControls(true)
        // Start the auto-hide timer
        const timer = setTimeout(() => {
          setShowControls(false)
        }, 3000)
        setMouseTimer(timer)
      }).catch(err => {
        console.error('Failed to enter fullscreen:', err)
        // Fallback to non-fullscreen mode
        setIsFullscreenActive(true)
        setShowControls(true)
      })
    } else if (!isVisible && isFullscreenActive) {
      // Exit fullscreen
      if (document.fullscreenElement) {
        document.exitFullscreen().then(() => {
          setIsFullscreenActive(false)
        }).catch(err => {
          console.error('Failed to exit fullscreen:', err)
          setIsFullscreenActive(false)
        })
      } else {
        setIsFullscreenActive(false)
      }
    }
  }, [isVisible, isFullscreenActive])

  // Monitor fullscreen state changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && isFullscreenActive) {
        // User pressed ESC or exited fullscreen via browser
        setIsFullscreenActive(false)
        onClose()
      }
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [isFullscreenActive, onClose])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isVisible) return

      // Prevent default for handled keys
      const handledKeys = ['Space', 'ArrowLeft', 'ArrowRight', 'KeyR', 'KeyF']
      if (handledKeys.includes(event.code)) {
        event.preventDefault()
      }

      switch (event.code) {
        case 'Escape':
          // Let the browser handle ESC for fullscreen exit
          // onClose will be called via fullscreenchange event
          break
        case 'Space':
          togglePlayPause()
          break
        case 'ArrowLeft':
          skip(-3, durationInFrames)
          break
        case 'ArrowRight':
          skip(3, durationInFrames)
          break
        case 'KeyR':
          // R key to reset to start
          resetToStart()
          break
        case 'KeyF':
          // F key to exit fullscreen (alternative to ESC)
          onClose()
          break
        case 'KeyC':
          // C key to toggle controls visibility
          event.preventDefault()
          setShowControls(!showControls)
          break
      }
    }

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown)
      document.addEventListener('mousemove', handleMouseMove)
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('mousemove', handleMouseMove)
      if (mouseTimer) {
        clearTimeout(mouseTimer)
      }
    }
  }, [isVisible, onClose, togglePlayPause, skip, durationInFrames, resetToStart, showControls, mouseTimer])

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (mouseTimer) {
        clearTimeout(mouseTimer)
      }
    }
  }, [mouseTimer])

  if (!isVisible) {
    return null
  }

  // Safety check for required context
  if (!editorContext || !overlays) {
    console.error('FullscreenVideoPlayer: Missing required context or overlays')
    return null
  }

  // Safety check for player dimensions
  if (playerWidth <= 0 || playerHeight <= 0) {
    return (
      <div
        ref={fullscreenContainer}
        className="fixed inset-0 z-[9999] bg-black flex items-center justify-center"
      >
        <div className="text-white text-lg">加载中...</div>
      </div>
    )
  }

  // Calculate optimal video size for fullscreen
  const screenWidth = window.innerWidth
  const screenHeight = window.innerHeight
  const videoAspectRatio = playerWidth / playerHeight
  const screenAspectRatio = screenWidth / screenHeight

  let displayWidth: number
  let displayHeight: number

  if (videoAspectRatio > screenAspectRatio) {
    // Video is wider than screen
    displayWidth = screenWidth * 0.9 // Leave some margin
    displayHeight = displayWidth / videoAspectRatio
  } else {
    // Video is taller than screen
    displayHeight = screenHeight * 0.9 // Leave some margin
    displayWidth = displayHeight * videoAspectRatio
  }

  const PLAYER_CONFIG = {
    durationInFrames: Math.round(durationInFrames),
    fps
  }

  return (
    <div
      ref={fullscreenContainer}
      className="fixed inset-0 z-[9999] bg-black flex items-center justify-center"
      onMouseMove={handleMouseMove}
    >
      {/* Video Player */}
      <div
        ref={playerWrapper}
        className="relative"
        style={{
          width: displayWidth,
          height: displayHeight,
          maxWidth: '90vw',
          maxHeight: '90vh'
        }}
      >
        <Player
          ref={playerRef}
          overflowVisible
          className="w-full h-full"
          component={RENDER_MODE ? Renderer : EditableRenderer}
          compositionWidth={playerWidth}
          compositionHeight={playerHeight}
          style={{
            width: '100%',
            height: '100%'
          }}
          durationInFrames={PLAYER_CONFIG.durationInFrames}
          fps={PLAYER_CONFIG.fps}
          errorFallback={() => <></>}
          inputProps={{
            overlays,
            playerMetadata: {
              durationInFrames: PLAYER_CONFIG.durationInFrames,
              fps: PLAYER_CONFIG.fps,
              width: playerWidth,
              height: playerHeight
            }
          } satisfies MainProps}
        />
      </div>

      {/* Fullscreen Controls Overlay */}
      <div
        className={`absolute inset-0 transition-opacity duration-300 ${
          showControls ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
      >
        {/* Top Controls - Exit Button and Shortcuts Info */}
        <div className="absolute top-6 right-6 flex items-center gap-4">
          {/* Keyboard Shortcuts Info */}
          <div className="text-white/70 text-xs hidden sm:block">
            <div className="bg-black/60 backdrop-blur-sm rounded-md px-3 py-2">
              <div className="space-y-1">
                <div>空格键: 播放/暂停</div>
                <div>← →: 快退/快进</div>
                <div>R: 重置 | F: 退出 | C: 显示/隐藏控件</div>
              </div>
            </div>
          </div>

          <Button
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="h-10 w-10 text-white hover:text-white hover:bg-white/20 rounded-full"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Bottom Controls */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <div className="flex items-center gap-4 bg-black/80 backdrop-blur-sm rounded-lg px-6 py-3">
            {/* Reset to Start */}
            <Button
              onClick={resetToStart}
              variant="ghost"
              size="sm"
              className="h-10 w-10 text-white hover:text-white hover:bg-white/20"
              title="重置到开始"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>

            {/* Skip Backward */}
            <Button
              onClick={() => skip(-3, durationInFrames)}
              variant="ghost"
              size="sm"
              className="h-10 w-10 text-white hover:text-white hover:bg-white/20"
              title="快退 3 秒"
            >
              <SkipBack className="h-5 w-5" />
            </Button>

            {/* Play/Pause */}
            <Button
              onClick={togglePlayPause}
              variant="ghost"
              size="sm"
              className="h-12 w-12 text-white hover:text-white hover:bg-white/20 rounded-full"
              title={isPlaying ? '暂停' : '播放'}
            >
              {isPlaying ? (
                <Pause className="h-6 w-6" />
              ) : (
                <Play className="h-6 w-6" />
              )}
            </Button>

            {/* Skip Forward */}
            <Button
              onClick={() => skip(3, durationInFrames)}
              variant="ghost"
              size="sm"
              className="h-10 w-10 text-white hover:text-white hover:bg-white/20"
              title="快进 3 秒"
            >
              <SkipForward className="h-5 w-5" />
            </Button>

            {/* Time Display */}
            <div className="flex items-center space-x-2 text-white text-sm font-medium ml-4">
              <span className="tabular-nums">{formatTime(currentFrame)}</span>
              <span className="text-white/50">/</span>
              <span className="tabular-nums text-white/70">{formatTime(durationInFrames)}</span>
            </div>

            {/* Exit Fullscreen */}
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="h-10 w-10 text-white hover:text-white hover:bg-white/20 ml-4"
              title="退出全屏"
            >
              <Minimize className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Click outside to show controls */}
      <div 
        className="absolute inset-0 cursor-none"
        onClick={e => {
          if (e.target === e.currentTarget) {
            setShowControls(!showControls)
          }
        }}
      />
    </div>
  )
}
